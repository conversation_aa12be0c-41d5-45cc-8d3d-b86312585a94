<template>
  <div class="lineage-layout">
    <!-- 主布局容器 -->
    <a-layout class="layout-container">
      <!-- 左侧面板 -->
      <a-layout-sider
        v-model:collapsed="leftCollapsed"
        :width="leftPanelWidth"
        :collapsed-width="0"
        :trigger="null"
        collapsible
        class="left-panel"
        theme="light"
      >
        <div class="left-panel-content">
          <!-- 工具栏区域 -->
          <div class="toolbar-section">
            <div class="toolbar-header">
              <h3>SQL 血缘分析</h3>
              <a-button
                type="text"
                :icon="leftCollapsed ? h(MenuUnfoldOutlined) : h(MenuFoldOutlined)"
                @click="toggleLeftPanel"
                class="collapse-btn"
              />
            </div>

            <!-- 数据库类型选择 -->
            <div class="control-group">
              <label class="control-label">数据库类型</label>
              <a-select
                v-model:value="selectedDbType"
                placeholder="选择数据库类型"
                style="width: 100%"
                @change="handleDbTypeChange"
              >
                <a-select-option value="mysql">MySQL</a-select-option>
                <a-select-option value="postgresql">PostgreSQL</a-select-option>
                <a-select-option value="oracle">Oracle</a-select-option>
                <a-select-option value="sqlserver">SQL Server</a-select-option>
                <a-select-option value="hive">Hive</a-select-option>
                <a-select-option value="spark">Spark SQL</a-select-option>
              </a-select>
            </div>

            <!-- 操作按钮组 -->
            <div class="control-group">
              <a-button type="primary" block @click="handleParseLineage" size="large">
                <template #icon><ShareAltOutlined /></template>
                解析血缘关系
              </a-button>
            </div>
          </div>

          <!-- SQL编辑器区域 -->
          <div class="editor-section">
            <SqlEditor
              v-model="sqlContent"
              :database-type="selectedDbType"
              :theme="theme"
              placeholder="请输入 SQL 语句..."
              @change="handleSqlChange"
              @format="handleSqlFormat"
              @clear="handleClearSql"
            />
          </div>
        </div>
      </a-layout-sider>

      <!-- 右侧主内容区域 -->
      <a-layout class="right-layout">
        <!-- 右侧头部控制栏 -->
        <a-layout-header class="right-header">
          <div class="header-content">
            <div class="header-left">
              <h2>数据血缘图谱</h2>
            </div>
            <div class="header-right">
              <!-- 控制开关 -->
              <a-space>
                <div class="control-item">
                  <label>字段级血缘</label>
                  <a-switch
                    v-model:checked="showFieldLevel"
                    @change="handleFieldLevelChange"
                    size="small"
                  />
                </div>
                <div class="control-item">
                  <label>完整链路</label>
                  <a-switch
                    v-model:checked="showFullLineage"
                    @change="handleFullLineageChange"
                    size="small"
                  />
                </div>
                <a-divider type="vertical" />
                <!-- 图谱工具按钮 -->
                <a-tooltip title="适应画布">
                  <a-button
                    type="text"
                    :icon="h(ExpandOutlined)"
                    @click="handleFitView"
                    size="small"
                  />
                </a-tooltip>
                <a-tooltip title="重置布局">
                  <a-button
                    type="text"
                    :icon="h(ReloadOutlined)"
                    @click="handleResetLayout"
                    size="small"
                  />
                </a-tooltip>
                <a-tooltip title="导出图片">
                  <a-button
                    type="text"
                    :icon="h(DownloadOutlined)"
                    @click="handleExportImage"
                    size="small"
                  />
                </a-tooltip>
                <a-tooltip title="切换缩略图">
                  <a-button
                    type="text"
                    :icon="h(BorderOutlined)"
                    @click="handleToggleMiniMap"
                    size="small"
                  />
                </a-tooltip>
                <a-divider type="vertical" />
                <!-- 布局方向控制 -->
                <a-dropdown :trigger="['click']">
                  <a-button type="text" size="small">
                    布局方向
                    <DownOutlined />
                  </a-button>
                  <template #overlay>
                    <a-menu @click="handleLayoutDirectionChange">
                      <a-menu-item key="LR">
                        <ArrowRightOutlined />
                        从左到右
                      </a-menu-item>
                      <a-menu-item key="TB">
                        <ArrowDownOutlined />
                        从上到下
                      </a-menu-item>
                      <a-menu-item key="RL">
                        <ArrowLeftOutlined />
                        从右到左
                      </a-menu-item>
                      <a-menu-item key="BT">
                        <ArrowUpOutlined />
                        从下到上
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
                <a-button
                  type="text"
                  :icon="h(SettingOutlined)"
                  @click="showSettings = true"
                  size="small"
                >
                  设置
                </a-button>
              </a-space>
            </div>
          </div>
        </a-layout-header>

        <!-- 图谱内容区域 -->
        <a-layout-content class="graph-content">
          <div class="graph-container" ref="graphContainer">
            <!-- 搜索框 -->
            <div class="graph-search" v-if="hasGraphData">
              <a-input
                v-model:value="searchKeyword"
                placeholder="搜索表名或字段名..."
                :prefix="h(SearchOutlined)"
                @change="handleSearch"
                @pressEnter="handleSearchEnter"
                class="search-input"
                size="small"
              />
              <div class="search-results" v-if="searchResults.length > 0">
                <div
                  v-for="result in searchResults"
                  :key="result.id"
                  class="search-result-item"
                  @click="handleSearchResultClick(result)"
                >
                  <div class="result-type">{{ result.type === 'table' ? '表' : '字段' }}</div>
                  <div class="result-content">
                    <div class="result-name">{{ result.name }}</div>
                    <div class="result-desc" v-if="result.tableName">{{ result.tableName }}</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 浮动工具栏 -->
            <div class="graph-toolbar" v-if="hasGraphData">
              <a-space direction="vertical">
                <a-tooltip title="放大" placement="left">
                  <a-button
                    type="primary"
                    :icon="h(ZoomInOutlined)"
                    @click="handleZoomIn"
                    size="small"
                    shape="circle"
                  />
                </a-tooltip>
                <a-tooltip title="缩小" placement="left">
                  <a-button
                    type="primary"
                    :icon="h(ZoomOutOutlined)"
                    @click="handleZoomOut"
                    size="small"
                    shape="circle"
                  />
                </a-tooltip>
                <a-tooltip title="适应画布" placement="left">
                  <a-button
                    type="primary"
                    :icon="h(ExpandOutlined)"
                    @click="handleFitView"
                    size="small"
                    shape="circle"
                  />
                </a-tooltip>
              </a-space>
            </div>

            <!-- 图谱渲染区域 -->
            <div class="graph-canvas" ref="graphCanvas" v-if="hasGraphData">
              <LineageGraph
                ref="lineageGraphRef"
                :data="lineageStore.g6GraphData"
                :width="graphCanvasWidth"
                :height="graphCanvasHeight"
                @graph-ready="handleGraphReady"
                @node-click="handleNodeClick"
                @edge-click="handleEdgeClick"
                @canvas-click="handleCanvasClick"
                @field-hover="handleFieldHover"
                @field-click="handleFieldClick"
                @field-leave="handleFieldLeave"
              />
            </div>

            <!-- 占位符 -->
            <div class="graph-placeholder" v-if="!hasGraphData">
              <div class="placeholder-content">
                <ShareAltOutlined class="placeholder-icon" />
                <h3>数据血缘图谱</h3>
                <p>请在左侧输入 SQL 语句并点击"解析血缘"按钮开始分析</p>
                <div class="placeholder-actions">
                  <a-button type="primary" @click="handleLoadSample">
                    加载示例数据
                  </a-button>
                </div>
              </div>
            </div>

            <!-- 加载状态 -->
            <div class="graph-loading" v-if="loading">
              <a-spin size="large" tip="正在解析血缘关系...">
                <div class="loading-content"></div>
              </a-spin>
            </div>
          </div>
        </a-layout-content>
      </a-layout>
    </a-layout>

    <!-- 设置抽屉 -->
    <a-drawer
      v-model:open="showSettings"
      title="图谱设置"
      placement="right"
      :width="320"
    >
      <div class="settings-content">
        <div class="setting-group">
          <h4>布局设置</h4>
          <div class="setting-item">
            <label>布局方向</label>
            <a-radio-group v-model:value="layoutDirection">
              <a-radio value="LR">从左到右</a-radio>
              <a-radio value="TB">从上到下</a-radio>
            </a-radio-group>
          </div>
        </div>

        <div class="setting-group">
          <h4>显示设置</h4>
          <div class="setting-item">
            <label>显示字段类型</label>
            <a-switch v-model:checked="showFieldTypes" />
          </div>
          <div class="setting-item">
            <label>显示表注释</label>
            <a-switch v-model:checked="showTableComments" />
          </div>
        </div>
      </div>
    </a-drawer>

    <!-- 字段详情面板 -->
    <FieldDetailDrawer
      v-model:visible="showFieldDetail"
      :field-data="selectedFieldData"
      @trace-lineage="handleTraceLineage"
      @highlight-field="handleHighlightField"
      @field-click="handleRelatedFieldClick"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, h } from 'vue'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  ShareAltOutlined,
  SettingOutlined,
  ExpandOutlined,
  ReloadOutlined,
  DownloadOutlined,
  SearchOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  BorderOutlined,
  DownOutlined,
  ArrowRightOutlined,
  ArrowDownOutlined,
  ArrowLeftOutlined,
  ArrowUpOutlined
} from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import { useLineageStore } from '@/stores/lineageStore'
import SqlEditor from './SqlEditor.vue'
import LineageGraph from './LineageGraph.vue'
import FieldDetailDrawer from './FieldDetailDrawer.vue'
import { runFieldEdgeTests } from '@/tests/fieldEdgeTest'
import { runAllFieldEdgeTests } from '@/tests/fieldEdgeUnitTest'
import { runCompleteDemo } from '@/tests/fieldEdgeDemo'
import { runFieldInteractionDemo, demoFieldInteractionEvents, demoFieldHighlight } from '@/tests/fieldInteractionDemo'
import type { LineageNode } from '@/types/lineage'

// 状态管理
const lineageStore = useLineageStore()

// 响应式数据
const leftCollapsed = ref(false)
const leftPanelWidth = ref(400)
const selectedDbType = ref('mysql')
const sqlContent = ref('')
const showFieldLevel = computed(() => lineageStore.showFieldLevelLineage)
const showFullLineage = computed(() => lineageStore.showCompleteLineage)
const showSettings = ref(false)
const layoutDirection = ref('LR')
const showFieldTypes = ref(true)
const showTableComments = ref(true)
const theme = ref<'light' | 'dark'>('light')
const searchKeyword = ref('')

// 字段交互相关状态
const showFieldDetail = ref(false)
const selectedFieldData = ref<LineageNode | null>(null)

// DOM引用
const graphContainer = ref<HTMLElement>()
const graphCanvas = ref<HTMLElement>()
const lineageGraphRef = ref<any>(null)

// 计算属性
const hasGraphData = computed(() => {
  return lineageStore.g6GraphData !== null && lineageStore.g6GraphData.nodes.length > 0
})

const loading = computed(() => lineageStore.loading)

// 图谱画布尺寸计算
const graphCanvasWidth = computed(() => {
  if (!graphCanvas.value) return 800
  return graphCanvas.value.clientWidth || 800
})

const graphCanvasHeight = computed(() => {
  if (!graphCanvas.value) return 600
  return graphCanvas.value.clientHeight || 600
})

const searchResults = computed(() => {
  return lineageStore.searchResults
})

// 方法
const toggleLeftPanel = () => {
  leftCollapsed.value = !leftCollapsed.value
}

const handleDbTypeChange = (value: string) => {
  console.log('数据库类型变更:', value)
  lineageStore.setDatabaseType(value as any)
}

const handleParseLineage = () => {
  if (!sqlContent.value.trim()) {
    message.warning('请先输入 SQL 语句')
    return
  }

  console.log('解析血缘关系:', sqlContent.value)
  message.success('血缘解析功能开发中...')
}

const handleClearSql = () => {
  sqlContent.value = ''
  message.info('已清空 SQL 内容')
}

const handleSqlChange = (value: string) => {
  // SQL内容变化处理
  console.log('SQL内容变化:', value)
  lineageStore.sqlText = value
}

const handleSqlFormat = (formattedSql: string) => {
  console.log('SQL格式化完成:', formattedSql)
  sqlContent.value = formattedSql
}

const handleFieldLevelChange = (checked: boolean) => {
  console.log('字段级血缘关系:', checked)
  if (checked !== lineageStore.showFieldLevelLineage) {
    lineageStore.toggleFieldLevelLineage()
  }
}

const handleFullLineageChange = (checked: boolean) => {
  console.log('完整血缘链路:', checked)
  if (checked !== lineageStore.showCompleteLineage) {
    lineageStore.toggleCompleteLineage()
  }
}

// 图谱控制方法
const handleFitView = () => {
  console.log('适应画布')
  if (lineageGraphRef.value && lineageGraphRef.value.isReady) {
    lineageGraphRef.value.fitView()
    message.success('已适应画布大小')
  } else {
    message.warning('图谱未就绪，请稍后再试')
  }
}

const handleResetLayout = () => {
  console.log('重置布局')
  if (lineageGraphRef.value && lineageGraphRef.value.isReady) {
    const success = lineageGraphRef.value.resetLayout()
    if (success) {
      message.success('已重置图谱布局')
    } else {
      message.error('重置布局失败')
    }
  } else {
    message.warning('图谱未就绪，请稍后再试')
  }
}

const handleToggleMiniMap = () => {
  console.log('切换缩略图')
  if (lineageGraphRef.value && lineageGraphRef.value.isReady) {
    lineageGraphRef.value.toggleMiniMap()
    const isVisible = lineageGraphRef.value.showMiniMap
    message.success(`缩略图已${isVisible ? '显示' : '隐藏'}`)
  } else {
    message.warning('图谱未就绪，请稍后再试')
  }
}

const handleExportImage = () => {
  console.log('导出图片')

  if (lineageGraphRef.value && lineageGraphRef.value.isReady) {
    // 显示导出选项对话框
    Modal.confirm({
      title: '导出图谱',
      content: '请选择导出格式',
      okText: 'PNG格式',
      cancelText: 'JPEG格式',
      onOk: () => {
        const result = lineageGraphRef.value.exportAsImage('png', `lineage-graph-${Date.now()}.png`)
        if (result) {
          message.success('PNG图片导出成功')
        } else {
          message.error('PNG图片导出失败')
        }
      },
      onCancel: () => {
        const result = lineageGraphRef.value.exportAsImage('jpeg', `lineage-graph-${Date.now()}.jpg`)
        if (result) {
          message.success('JPEG图片导出成功')
        } else {
          message.error('JPEG图片导出失败')
        }
      }
    })
  } else {
    message.warning('图谱未就绪，请稍后再试')
  }
}

const handleZoomIn = () => {
  console.log('放大')
  if (lineageGraphRef.value && lineageGraphRef.value.isReady) {
    lineageGraphRef.value.zoomIn()
    const currentZoom = lineageGraphRef.value.getCurrentZoom()
    message.success(`已放大，当前缩放比例: ${(currentZoom * 100).toFixed(0)}%`)
  } else {
    message.warning('图谱未就绪，请稍后再试')
  }
}

const handleZoomOut = () => {
  console.log('缩小')
  if (lineageGraphRef.value && lineageGraphRef.value.isReady) {
    lineageGraphRef.value.zoomOut()
    const currentZoom = lineageGraphRef.value.getCurrentZoom()
    message.success(`已缩小，当前缩放比例: ${(currentZoom * 100).toFixed(0)}%`)
  } else {
    message.warning('图谱未就绪，请稍后再试')
  }
}

// 布局方向变更处理
const handleLayoutDirectionChange = ({ key }: { key: string }) => {
  console.log('切换布局方向:', key)
  if (lineageGraphRef.value && lineageGraphRef.value.isReady) {
    const directionMap = {
      'LR': '从左到右',
      'TB': '从上到下',
      'RL': '从右到左',
      'BT': '从下到上'
    }

    lineageGraphRef.value.changeLayoutDirection(key as 'LR' | 'TB' | 'RL' | 'BT')
    message.success(`布局方向已切换为: ${directionMap[key as keyof typeof directionMap]}`)
  } else {
    message.warning('图谱未就绪，请稍后再试')
  }
}

const handleLoadSample = () => {
  console.log('加载示例数据')
  lineageStore.loadSampleData()
  message.success('示例数据加载完成')

  // 运行字段级连线测试
  setTimeout(() => {
    console.log('🔍 检查加载的数据结构:')
    console.log('G6图数据:', lineageStore.g6GraphData)
    if (lineageStore.g6GraphData) {
      console.log('边数据详情:')
      lineageStore.g6GraphData.edges.forEach((edge, index) => {
        console.log(`边 ${index + 1}:`, {
          id: edge.id,
          source: edge.source,
          target: edge.target,
          type: edge.type,
          sourceField: edge.sourceField,
          targetField: edge.targetField,
          transformType: edge.transformType,
          label: edge.label
        })
      })
    }
  }, 500)
}

// 搜索相关方法
const handleSearch = () => {
  lineageStore.setSearchKeyword(searchKeyword.value)
}

const handleSearchEnter = () => {
  if (searchResults.value.length > 0) {
    handleSearchResultClick(searchResults.value[0])
  }
}

const handleSearchResultClick = (result: any) => {
  console.log('搜索结果点击:', result)

  if (lineageGraphRef.value && lineageGraphRef.value.isReady) {
    // 使用图谱组件的定位功能
    const success = lineageGraphRef.value.locateToNode(result.id, true)
    if (success) {
      message.success(`已定位到${result.type === 'table' ? '表' : '字段'}: ${result.name}`)
      // 清空搜索关键词以隐藏搜索结果
      searchKeyword.value = ''
    } else {
      message.error(`无法定位到${result.type === 'table' ? '表' : '字段'}: ${result.name}`)
    }
  } else {
    message.warning('图谱未就绪，请稍后再试')
  }
}

// 图谱事件处理方法
const handleGraphReady = (graph: any) => {
  console.log('图谱初始化完成:', graph)
  message.success('图谱加载完成')
}

const handleNodeClick = (nodeId: string, nodeData: any) => {
  console.log('节点点击:', nodeId, nodeData)
  message.info(`点击了节点: ${nodeData?.label || nodeId}`)
  // 这里可以实现节点详情展示等功能
}

const handleEdgeClick = (edgeId: string, edgeData: any) => {
  console.log('边点击:', edgeId, edgeData)
  message.info(`点击了连线: ${edgeData?.label || edgeId}`)
  // 这里可以实现连线详情展示等功能
}

// 字段事件处理方法
const handleFieldHover = (fieldId: string, fieldData: any, event: any) => {
  console.log('字段悬浮:', fieldId, fieldData)
  // 更新状态管理中的悬浮字段
  lineageStore.setHoveredNode(fieldId)

  // 可以在这里触发Tooltip显示
  // TODO: 实现字段Tooltip组件
}

const handleFieldClick = (fieldId: string, fieldData: any, event: any) => {
  console.log('字段点击:', fieldId, fieldData)
  message.info(`点击了字段: ${fieldData?.fieldName || fieldId}`)

  // 更新状态管理中的选中字段
  lineageStore.selectNode(fieldId)

  // 显示字段详情面板
  selectedFieldData.value = fieldData
  showFieldDetail.value = true

  // 高亮字段和相关连线
  if (lineageGraphRef.value) {
    lineageGraphRef.value.setFieldHighlight(fieldId, true)
  }
}

const handleFieldLeave = (fieldId: string) => {
  console.log('字段离开:', fieldId)
  // 清除悬浮状态
  lineageStore.setHoveredNode(null)
}

const handleCanvasClick = () => {
  console.log('画布点击')
  // 清除选中状态等
  lineageStore.clearSelection()
  showFieldDetail.value = false
  selectedFieldData.value = null

  // 清除所有高亮
  if (lineageGraphRef.value) {
    // 清除字段高亮
    if (lineageStore.hoveredNode) {
      lineageGraphRef.value.setFieldHighlight(lineageStore.hoveredNode, false)
    }
  }
}

// 字段详情面板事件处理
const handleTraceLineage = (fieldData: LineageNode) => {
  console.log('追踪字段血缘:', fieldData)
  message.success(`开始追踪字段 ${fieldData.fieldName} 的血缘路径`)

  // 实现路径追踪高亮功能
  if (lineageGraphRef.value) {
    // 高亮字段和所有相关连线
    lineageGraphRef.value.setFieldHighlight(fieldData.id, true)
    lineageGraphRef.value.highlightRelatedEdges(fieldData.id, true)

    // 3秒后清除高亮
    setTimeout(() => {
      if (lineageGraphRef.value) {
        lineageGraphRef.value.setFieldHighlight(fieldData.id, false)
        lineageGraphRef.value.highlightRelatedEdges(fieldData.id, false)
      }
    }, 3000)
  }
}

const handleHighlightField = (fieldData: LineageNode) => {
  console.log('高亮字段:', fieldData)
  message.success(`字段 ${fieldData.fieldName} 已高亮`)

  if (lineageGraphRef.value) {
    lineageGraphRef.value.setFieldHighlight(fieldData.id, true)

    // 2秒后清除高亮
    setTimeout(() => {
      if (lineageGraphRef.value) {
        lineageGraphRef.value.setFieldHighlight(fieldData.id, false)
      }
    }, 2000)
  }
}

const handleRelatedFieldClick = (fieldData: LineageNode) => {
  console.log('点击相关字段:', fieldData)
  // 切换到新的字段详情
  selectedFieldData.value = fieldData
  message.info(`切换到字段: ${fieldData.fieldName}`)
}

// 响应式处理
const handleResize = () => {
  // 处理窗口大小变化
  if (window.innerWidth < 768) {
    leftPanelWidth.value = 300
  } else if (window.innerWidth < 1200) {
    leftPanelWidth.value = 350
  } else {
    leftPanelWidth.value = 400
  }
}

// 生命周期
onMounted(() => {
  window.addEventListener('resize', handleResize)
  handleResize() // 初始化时调用一次

  // 运行字段级连线功能测试
  setTimeout(() => {
    console.log('🧪 运行字段级连线功能测试...')
    runFieldEdgeTests()

    // 运行详细的单元测试
    console.log('\n🔬 运行详细单元测试...')
    runAllFieldEdgeTests()
  }, 1000)

  // 将测试函数暴露到全局，方便在浏览器控制台中调用
  ;(window as any).testFieldEdges = () => {
    console.log('🔍 手动测试字段级连线功能')
    const testResult = runAllFieldEdgeTests()
    console.log('测试结果:', testResult)
    return testResult
  }

  // 暴露演示函数
  ;(window as any).demoFieldEdges = () => {
    console.log('🎯 字段级连线功能演示')
    const demoResult = runCompleteDemo()
    console.log('演示结果:', demoResult)
    return demoResult
  }

  // 暴露字段交互演示函数
  ;(window as any).demoFieldInteraction = () => {
    console.log('🎮 字段交互功能演示')
    const demoResult = runFieldInteractionDemo()
    console.log('演示结果:', demoResult)
    return demoResult
  }

  ;(window as any).demoFieldEvents = () => {
    console.log('🎭 字段交互事件演示')
    demoFieldInteractionEvents()
  }

  ;(window as any).demoFieldHighlight = () => {
    console.log('🌟 字段高亮功能演示')
    const result = demoFieldHighlight()
    console.log('高亮演示结果:', result)
    return result
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.lineage-layout {
  height: 100vh;
  width: 100%;
}

.layout-container {
  height: 100%;
}

/* 左侧面板样式 */
.left-panel {
  border-right: 1px solid #f0f0f0;
  background: #fafafa;
}

.left-panel-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.toolbar-section {
  flex-shrink: 0;
  margin-bottom: 16px;
}

.toolbar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e8e8e8;
}

.toolbar-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.collapse-btn {
  padding: 4px;
}

.control-group {
  margin-bottom: 16px;
}

.control-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #595959;
}

/* 编辑器区域样式 */
.editor-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.editor-container {
  flex: 1;
  min-height: 0;
}

.sql-editor {
  height: 100% !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
}

/* 右侧布局样式 */
.right-layout {
  background: #fff;
}

.right-header {
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  padding: 0 24px;
  height: 64px;
  line-height: 64px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.header-left h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}

.header-right .control-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-right .control-item label {
  font-size: 14px;
  color: #595959;
  white-space: nowrap;
}

/* 图谱内容区域 */
.graph-content {
  padding: 0;
  background: #f5f5f5;
}

.graph-container {
  height: 100%;
  width: 100%;
  position: relative;
  background: #fff;
  overflow: hidden;
}

.graph-canvas {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

/* 搜索框样式 */
.graph-search {
  position: absolute;
  top: 16px;
  left: 16px;
  z-index: 10;
  width: 280px;
}

.search-input {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-height: 200px;
  overflow-y: auto;
  margin-top: 4px;
}

.search-result-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
}

.search-result-item:hover {
  background-color: #f5f5f5;
}

.search-result-item:last-child {
  border-bottom: none;
}

.result-type {
  flex-shrink: 0;
  width: 32px;
  height: 20px;
  background: #1890ff;
  color: #fff;
  font-size: 12px;
  text-align: center;
  line-height: 20px;
  border-radius: 4px;
  margin-right: 8px;
}

.result-content {
  flex: 1;
  min-width: 0;
}

.result-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.result-desc {
  font-size: 12px;
  color: #8c8c8c;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 浮动工具栏样式 */
.graph-toolbar {
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 10;
}

.graph-toolbar .ant-btn {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 8px;
}

.graph-toolbar .ant-btn:last-child {
  margin-bottom: 0;
}

.graph-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-content {
  text-align: center;
  color: #8c8c8c;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #d9d9d9;
}

.placeholder-content h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 500;
  color: #595959;
}

.placeholder-content p {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #8c8c8c;
}

.placeholder-actions {
  margin-top: 16px;
}

/* 加载状态样式 */
.graph-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
}

.loading-content {
  width: 100px;
  height: 100px;
}

/* 设置抽屉样式 */
.settings-content {
  padding: 8px 0;
}

.setting-group {
  margin-bottom: 24px;
}

.setting-group h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.setting-item label {
  font-size: 14px;
  color: #595959;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    height: auto;
    padding: 12px 0;
  }

  .header-left {
    margin-bottom: 8px;
  }

  .header-right .control-item {
    font-size: 12px;
  }

  .right-header {
    height: auto;
    line-height: normal;
    padding: 12px 16px;
  }
}

@media (max-width: 576px) {
  .left-panel-content {
    padding: 12px;
  }

  .toolbar-header h3 {
    font-size: 14px;
  }

  .header-right {
    width: 100%;
  }

  .header-right .ant-space {
    width: 100%;
    justify-content: space-between;
  }
}
</style>
