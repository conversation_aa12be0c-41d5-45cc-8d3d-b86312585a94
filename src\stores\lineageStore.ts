/**
 * 血缘图数据状态管理
 * 使用 Pinia 管理图谱数据状态
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  LineageData,
  G6GraphData,
  LineageNode,
  LineageEdge,
  TableInfo,
  SearchResult,
  PathTraceResult,
  ThemeConfig
} from '@/types/lineage'
import { DatabaseType } from '@/types/lineage'
import { transformToG6Data } from '@/utils/graphDataTransform'
import { createSampleLineageData } from '@/utils/sqlParser'

export const useLineageStore = defineStore('lineage', () => {
  // ===== 状态定义 =====

  // 原始血缘数据
  const lineageData = ref<LineageData | null>(null)

  // G6图数据
  const g6GraphData = ref<G6GraphData | null>(null)

  // 当前SQL文本
  const sqlText = ref<string>('')

  // 数据库类型
  const databaseType = ref<DatabaseType>(DatabaseType.MYSQL)

  // 加载状态
  const loading = ref<boolean>(false)

  // 错误信息
  const error = ref<string | null>(null)

  // 图谱配置
  const graphConfig = ref({
    width: 800,
    height: 600,
    fitView: true,
    fitViewPadding: 20,
    animate: true,
    animateCfg: {
      duration: 500,
      easing: 'easeInOutCubic'
    }
  })

  // 控制开关状态
  const showFieldLevelLineage = ref<boolean>(true)
  const showCompleteLineage = ref<boolean>(true)

  // 选中的节点和边
  const selectedNodes = ref<string[]>([])
  const selectedEdges = ref<string[]>([])

  // 悬浮的节点
  const hoveredNode = ref<string | null>(null)

  // 搜索关键词
  const searchKeyword = ref<string>('')

  // 主题配置 - 现代扁平化设计
  const theme = ref<ThemeConfig>({
    mode: 'light',
    colors: {
      primary: '#1890ff',
      secondary: '#52c41a',
      background: '#ffffff',
      surface: '#fafafa',
      text: '#262626',
      textSecondary: '#595959',
      border: 'rgba(0, 0, 0, 0.06)',
      shadow: 'rgba(0, 0, 0, 0.08)',
      // 节点样式 - 现代卡片设计
      nodeBackground: '#ffffff',
      nodeHeaderBackground: '#fafafa',
      nodeBorder: 'rgba(0, 0, 0, 0.06)',
      nodeText: '#262626',
      nodeFieldText: '#595959',
      nodeShadow: 'rgba(0, 0, 0, 0.08)',
      // 边样式 - 更柔和的颜色
      edgeStroke: '#d9d9d9',
      edgeActiveStroke: '#1890ff',
      edgeLabel: '#8c8c8c',
      // 交互状态 - 更明显的反馈
      hover: '#f5f5f5',
      active: '#e6f7ff',
      selected: '#1890ff',
      // 字段状态颜色
      fieldHover: '#f0f0f0',
      fieldActive: '#e6f7ff',
      fieldSelected: '#bae7ff'
    }
  })

  // ===== 计算属性 =====

  // 所有表信息
  const tables = computed(() => {
    return lineageData.value?.tables || {}
  })

  // 所有节点
  const nodes = computed(() => {
    return lineageData.value?.nodes || []
  })

  // 所有边
  const edges = computed(() => {
    return lineageData.value?.edges || []
  })

  // 表数量
  const tableCount = computed(() => {
    return Object.keys(tables.value).length
  })

  // 字段数量
  const fieldCount = computed(() => {
    return nodes.value.length
  })

  // 血缘关系数量
  const relationCount = computed(() => {
    return edges.value.length
  })

  // 搜索结果
  const searchResults = computed((): SearchResult[] => {
    if (!searchKeyword.value.trim()) return []

    const keyword = searchKeyword.value.toLowerCase()
    const results: SearchResult[] = []

    // 搜索表名
    Object.values(tables.value).forEach(table => {
      if (table.name.toLowerCase().includes(keyword)) {
        results.push({
          type: 'table',
          id: table.name,
          name: table.name,
          description: table.description,
          matchScore: table.name.toLowerCase().indexOf(keyword) === 0 ? 1 : 0.8
        })
      }
    })

    // 搜索字段名
    nodes.value.forEach(node => {
      if (node.fieldName.toLowerCase().includes(keyword)) {
        results.push({
          type: 'field',
          id: node.id,
          name: node.fieldName,
          tableName: node.tableName,
          description: node.description,
          matchScore: node.fieldName.toLowerCase().indexOf(keyword) === 0 ? 1 : 0.8
        })
      }
    })

    // 按匹配度排序
    return results.sort((a, b) => b.matchScore - a.matchScore)
  })

  // ===== 方法定义 =====

  /**
   * 设置血缘数据
   * @param data 血缘数据
   */
  const setLineageData = (data: LineageData) => {
    lineageData.value = data
    g6GraphData.value = transformToG6Data(data)
    error.value = null
  }

  /**
   * 解析SQL并生成血缘数据
   * @param sql SQL语句
   */
  const parseSqlLineage = async (sql: string) => {
    loading.value = true
    error.value = null

    try {
      // 更新SQL文本
      sqlText.value = sql

      // 这里应该调用后端API解析SQL
      // 目前使用示例数据
      await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用

      const sampleData = createSampleLineageData(sql)
      setLineageData(sampleData)

    } catch (err) {
      error.value = err instanceof Error ? err.message : '解析SQL失败'
    } finally {
      loading.value = false
    }
  }

  /**
   * 加载示例数据
   */
  const loadSampleData = () => {
    const sampleData = createSampleLineageData()
    setLineageData(sampleData)
    sqlText.value = sampleData.metadata?.sqlText || ''
  }

  /**
   * 清空数据
   */
  const clearData = () => {
    lineageData.value = null
    g6GraphData.value = null
    sqlText.value = ''
    selectedNodes.value = []
    selectedEdges.value = []
    hoveredNode.value = null
    error.value = null
  }

  /**
   * 设置数据库类型
   * @param type 数据库类型
   */
  const setDatabaseType = (type: DatabaseType) => {
    databaseType.value = type
  }

  /**
   * 设置图谱配置
   * @param config 配置对象
   */
  const setGraphConfig = (config: Partial<typeof graphConfig.value>) => {
    graphConfig.value = { ...graphConfig.value, ...config }
  }

  /**
   * 切换字段级血缘显示
   */
  const toggleFieldLevelLineage = () => {
    showFieldLevelLineage.value = !showFieldLevelLineage.value
  }

  /**
   * 切换完整血缘链路显示
   */
  const toggleCompleteLineage = () => {
    showCompleteLineage.value = !showCompleteLineage.value
  }

  /**
   * 选中节点
   * @param nodeId 节点ID
   * @param multiple 是否多选
   */
  const selectNode = (nodeId: string, multiple: boolean = false) => {
    if (multiple) {
      if (selectedNodes.value.includes(nodeId)) {
        selectedNodes.value = selectedNodes.value.filter(id => id !== nodeId)
      } else {
        selectedNodes.value.push(nodeId)
      }
    } else {
      selectedNodes.value = [nodeId]
    }
  }

  /**
   * 选中边
   * @param edgeId 边ID
   * @param multiple 是否多选
   */
  const selectEdge = (edgeId: string, multiple: boolean = false) => {
    if (multiple) {
      if (selectedEdges.value.includes(edgeId)) {
        selectedEdges.value = selectedEdges.value.filter(id => id !== edgeId)
      } else {
        selectedEdges.value.push(edgeId)
      }
    } else {
      selectedEdges.value = [edgeId]
    }
  }

  /**
   * 清空选择
   */
  const clearSelection = () => {
    selectedNodes.value = []
    selectedEdges.value = []
  }

  /**
   * 设置悬浮节点
   * @param nodeId 节点ID
   */
  const setHoveredNode = (nodeId: string | null) => {
    hoveredNode.value = nodeId
  }

  /**
   * 设置搜索关键词
   * @param keyword 关键词
   */
  const setSearchKeyword = (keyword: string) => {
    searchKeyword.value = keyword
  }

  /**
   * 切换主题
   * @param mode 主题模式
   */
  const setTheme = (mode: 'light' | 'dark') => {
    if (mode === 'dark') {
      theme.value = {
        mode: 'dark',
        colors: {
          primary: '#1890ff',
          secondary: '#52c41a',
          background: '#141414',
          surface: '#1f1f1f',
          text: '#ffffff',
          textSecondary: 'rgba(255, 255, 255, 0.85)',
          border: 'rgba(255, 255, 255, 0.08)',
          shadow: 'rgba(0, 0, 0, 0.3)',
          // 节点样式 - 深色主题
          nodeBackground: '#1f1f1f',
          nodeHeaderBackground: '#262626',
          nodeBorder: 'rgba(255, 255, 255, 0.08)',
          nodeText: '#ffffff',
          nodeFieldText: 'rgba(255, 255, 255, 0.85)',
          nodeShadow: 'rgba(0, 0, 0, 0.3)',
          // 边样式 - 深色主题
          edgeStroke: '#434343',
          edgeActiveStroke: '#1890ff',
          edgeLabel: 'rgba(255, 255, 255, 0.65)',
          // 交互状态 - 深色主题
          hover: '#262626',
          active: '#003a8c',
          selected: '#1890ff',
          // 字段状态颜色 - 深色主题
          fieldHover: '#262626',
          fieldActive: '#003a8c',
          fieldSelected: '#0050b3'
        }
      }
    } else {
      theme.value = {
        mode: 'light',
        colors: {
          primary: '#1890ff',
          secondary: '#52c41a',
          background: '#ffffff',
          surface: '#fafafa',
          text: '#262626',
          textSecondary: '#595959',
          border: 'rgba(0, 0, 0, 0.06)',
          shadow: 'rgba(0, 0, 0, 0.08)',
          // 节点样式 - 现代卡片设计
          nodeBackground: '#ffffff',
          nodeHeaderBackground: '#fafafa',
          nodeBorder: 'rgba(0, 0, 0, 0.06)',
          nodeText: '#262626',
          nodeFieldText: '#595959',
          nodeShadow: 'rgba(0, 0, 0, 0.08)',
          // 边样式 - 更柔和的颜色
          edgeStroke: '#d9d9d9',
          edgeActiveStroke: '#1890ff',
          edgeLabel: '#8c8c8c',
          // 交互状态 - 更明显的反馈
          hover: '#f5f5f5',
          active: '#e6f7ff',
          selected: '#1890ff',
          // 字段状态颜色
          fieldHover: '#f0f0f0',
          fieldActive: '#e6f7ff',
          fieldSelected: '#bae7ff'
        }
      }
    }
  }

  // 返回状态和方法
  return {
    // 状态
    lineageData,
    g6GraphData,
    sqlText,
    databaseType,
    loading,
    error,
    graphConfig,
    showFieldLevelLineage,
    showCompleteLineage,
    selectedNodes,
    selectedEdges,
    hoveredNode,
    searchKeyword,
    theme,

    // 计算属性
    tables,
    nodes,
    edges,
    tableCount,
    fieldCount,
    relationCount,
    searchResults,

    // 方法
    setLineageData,
    parseSqlLineage,
    loadSampleData,
    clearData,
    setDatabaseType,
    setGraphConfig,
    toggleFieldLevelLineage,
    toggleCompleteLineage,
    selectNode,
    selectEdge,
    clearSelection,
    setHoveredNode,
    setSearchKeyword,
    setTheme
  }
})
